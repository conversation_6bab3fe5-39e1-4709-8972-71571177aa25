import request from '@/utils/request';

export const PMC_REPORT_BASE_URL = '/pmc/report';

/**
 * 获取统计信息数据
 * @param data 查询参数
 */
export function getStatis7Data(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis7/search`,
    method: 'get',
    params: data,
    timeout: 3 * 60 * 1000
  });
}

/**
 * 导出统计信息数据
 * @param data 查询参数
 */
export function exportStatis7Data(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis7/export`,
    method: 'get',
    params: data,
    timeout: 5 * 60 * 1000
  });
}

/**
 * 获取厂商列表
 */
export function getSupplierList() {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis7/suppliers`,
    method: 'get',
    timeout: 30 * 1000
  });
}

/**
 * 获取客户分类列表
 */
export function getCustomerCategoryList() {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis7/customerCategories`,
    method: 'get',
    timeout: 30 * 1000
  });
}

/**
 * 获取PMC跟单员列表
 */
export function getPmcFollowerList() {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis7/pmcFollowers`,
    method: 'get',
    timeout: 30 * 1000
  });
}
