<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="8">
            <el-form-item label="外发日期">
              <el-date-picker
                v-model="searchForm.outsourceDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 260px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="MO单号">
              <el-input v-model="searchForm.moNumber" placeholder="" style="width: 140px" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="厂商简称">
              <el-input v-model="searchForm.supplierName" placeholder="" style="width: 140px" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="MO状态">
              <el-input v-model="searchForm.moStatus" placeholder="" style="width: 120px" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="8">
            <el-form-item label="要求日期">
              <el-date-picker
                v-model="searchForm.deliveryDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 260px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="客户代码">
              <el-input v-model="searchForm.customerCode" placeholder="" style="width: 140px" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="关闭否">
              <el-select v-model="searchForm.closeStore" placeholder="" style="width: 140px">
                <el-option label="是" value="Y" />
                <el-option label="否" value="N" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户分类" class="text-red-500">
              <el-select v-model="searchForm.customerCategory" placeholder="" style="width: 140px">
                <el-option label="自动化" value="自动化" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="24" class="flex justify-end gap-3">
            <el-button type="primary" :loading="loading" @click="handleQuery" size="default">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset" size="default">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport" :disabled="isExportDisabled" size="default">
              <el-icon>
                <Download />
              </el-icon>
              导出
            </el-button>
          </el-col>
        </el-row>


      </el-form>
    </el-card>

    <!-- 统计信息标题 -->
    <div class="mb-4">
      <span class="text-red-500 font-semibold">统计信息</span>
    </div>

    <!-- 统计表格区域 -->
    <el-card class="shadow-md">
      <el-table :data="paginatedStatisData" border stripe>
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="utilizationCenter" label="利用中心" width="120" align="center" />
        <el-table-column prop="pmcFollower" label="PMC跟单员" width="120" align="center" />
        <el-table-column prop="statistics" label="统计" width="100" align="center" />
        <el-table-column prop="deliveryAchievement" label="交期达成" width="100" align="center" />
        <el-table-column prop="achievementRate" label="达成率" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.achievementRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="pendingConfirmation" label="待确认是否对账" width="150" align="center" />
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="statisData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElCheckbox,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElPagination,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Download, Refresh, Search } from '@element-plus/icons-vue';
import { exportToExcel } from '@/utils/excel';



// 获取默认日期范围（当天的前7天到当天）
const getDefaultDateRange = () => {
  const today = new Date();
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7);

  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return [formatDate(sevenDaysAgo), formatDate(today)];
};

// 响应式表单数据
const searchForm = reactive({
  outsourceDateRange: getDefaultDateRange(),
  deliveryDateRange: getDefaultDateRange(),
  moNumber: '',
  supplierName: '',
  moStatus: '',
  customerCode: '',
  closeStore: 'N',
  customerCategory: '自动化'
});

// 统计数据
const statisData = ref([
  {
    id: 1,
    utilizationCenter: 'BU320',
    pmcFollower: '刘爱梅',
    statistics: 2,
    deliveryAchievement: 2,
    achievementRate: 100,
    pendingConfirmation: ''
  },
  {
    id: 2,
    utilizationCenter: 'BU320',
    pmcFollower: '王志梅',
    statistics: 1,
    deliveryAchievement: 1,
    achievementRate: 100,
    pendingConfirmation: ''
  }
]);

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);

// 计算分页后的统计数据
const paginatedStatisData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return statisData.value.slice(start, end);
});

// 加载状态
const loading = ref(false);
const exportLoading = ref(false);

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    // 这里应该调用实际的API
    // const res = await getStatis7Data(searchForm);
    // statisData.value = res.data || [];

    // 模拟查询延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success('查询成功');
  } catch (err: any) {
    ElMessage.error('查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.outsourceDateRange = getDefaultDateRange();
  searchForm.deliveryDateRange = getDefaultDateRange();
  searchForm.moNumber = '';
  searchForm.supplierName = '';
  searchForm.moStatus = '';
  searchForm.customerCode = '';
  searchForm.closeStore = '';
  searchForm.customerCategory = '自动化';
  currentPage.value = 1;
};

// 导出方法
const handleExport = async () => {
  exportLoading.value = true;
  try {
    if (statisData.value.length === 0) {
      ElMessage.warning('没有数据可以导出');
      return;
    }

    const exportData = statisData.value.map((item) => ({
      'ID': item.id,
      '利用中心': item.utilizationCenter,
      'PMC跟单员': item.pmcFollower,
      '统计': item.statistics,
      '交期达成': item.deliveryAchievement,
      '达成率': item.achievementRate + '%',
      '待确认是否对账': item.pendingConfirmation
    }));

    const headers = {
      'ID': 'ID',
      '利用中心': '利用中心',
      'PMC跟单员': 'PMC跟单员',
      '统计': '统计',
      '交期达成': '交期达成',
      '达成率': '达成率',
      '待确认是否对账': '待确认是否对账'
    };

    const fileName = `统计信息_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '统计信息', '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 计算导出按钮是否禁用
const isExportDisabled = computed(() => {
  return statisData.value.length === 0;
});

onMounted(() => {
  // 初始化数据
});
</script>

<style scoped>
/* 表格行选中样式 */
.el-table__row:hover {
  background-color: #f5f7fa !important;
}

.el-table__row.current-row {
  background-color: #e6f7ff !important;
}
</style>
